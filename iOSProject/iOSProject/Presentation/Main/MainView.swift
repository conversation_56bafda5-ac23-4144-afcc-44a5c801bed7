//
//  ContentView.swift
//  iOSProject
//
//  Created by Apple on 08/08/2025.
//

import SwiftUI

struct MainView: View {
    @EnvironmentObject private var navigationService:NavigationService
    @EnvironmentObject private var dependencyContainer: DependencyContainer

    var body: some View {
        Group {
            switch navigationService.currentView {
            case .authentication:
                AuthenticationView.build(container: dependencyContainer)
                    .transition(.backslide)
            case .mainNavigation:
                DashboardView()
                    .transition(.backslide)
            }
        }
        .tint(.appMain)
        .toastView(toast: $navigationService.toast)
        .themedBackground()
        .animation(.easeInOut, value: navigationService.currentView)
    }
}

#Preview {
    MainView()
        .environmentObject(ThemeManager())
        .environmentObject(DependencyContainer())
}
