import SwiftUI

// MARK: - Notification View

struct NotificationView: View {
    var body: some View {
        GeometryReader { geo in

            let size = geo.size

            MainScrollBody {
                VStack(spacing: 0) {
                    // a placeholder view

                    Text("Notification View")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .padding()
                }
                .frame(width: size.width, height: size.height, alignment: .center)
                .themedBackground()
            }
        }
    }
}

// MARK: - Preview

struct NotificationView_Previews: PreviewProvider {
    static var previews: some View {
        NotificationView()
            .environmentObject(ThemeManager())
    }
}
