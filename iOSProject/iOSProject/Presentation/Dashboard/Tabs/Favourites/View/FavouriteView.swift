import SwiftUI

// MARK: - Favourite View

struct FavouriteView: View {
    var body: some View {
        GeometryReader { geo in
            
            let size = geo.size
            
            MainScrollBody {
                VStack(spacing: 0) {
                    // a placeholder view
                    
                    Text("Favourite View")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .padding()
                }
                .frame(width: size.width, height: size.height, alignment: .center)
                .themedBackground()
        }
        }
    }
}
