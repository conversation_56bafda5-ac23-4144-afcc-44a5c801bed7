import SwiftUI

// MARK: - Feature Item View
struct FeatureItemView: View {
    let feature: FeatureItem
    @Environment(\.appColors) var colors
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16.h) {
            // Icon Container
            iconContainer
            
            // Title
            Text(feature.title)
                .headline18Medium()
                .themedTextColor(.primary)
            
            // Description
            Text(feature.description)
                .title16Regular()
                .themedTextColor(.secondary)
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Icon Container
    private var iconContainer: some View {
        ZStack {
            // Outer border circle
            Circle()
                .fill(Color(hex: "F9F5FF"))
                .frame(width: 52.h, height: 52.h)
            
            // Inner circle
            Circle()
                .fill(Color(hex: "F4EBFF"))
                .frame(width: 40.h, height: 40.h)
            
            // Icon
            Image(systemName: feature.icon)
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(colors.brandPrimary)
        }
    }
}

// MARK: - Preview
struct FeatureItemView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 32) {
            FeatureItemView(
                feature: FeatureItem(
                    icon: "chart.pie",
                    title: "Access to daily analytics",
                    description: "Optimize the way you recover, train, and sleep with daily reporting on mobile and desktop apps."
                )
            )
            
            FeatureItemView(
                feature: FeatureItem(
                    icon: "bolt",
                    title: "Measure recovery",
                    description: "The most advanced sleep tracking technology available today."
                )
            )
        }
        .padding()
        .environmentObject(ThemeManager())
    }
}
