//
//  TabModel.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import SwiftUI

enum Tab: Identifiable, CaseIterable, Hashable {
    case home, favourite, news, notification

    var id: Self { self }

    var image: ImageResource {
        switch self {
        case .home: .houseIcon
        case .favourite: .heartIcon
        case .news: .notepadIcon
        case .notification: .bellIcon
        }
    }

    var title: String {
        let title: String = {
            switch self {
            case .home: "Home"
            case .favourite: "Favourite"
            case .news: "News"
            case .notification: "Notification"
            }
        }()

        return title
    }

    var index: Int {
        return Tab.allCases.firstIndex(of: self) ?? 0
    }
}


extension Tab {
    @ViewBuilder @MainActor
    func view(with container: DependencyContainer) -> some View {
        switch self {
        case .home:
            HomeView.build(container: container)
        case .favourite:
            FavouriteView()
        case .news:
            NewsView()
        case .notification:
            NotificationView()

        }
    }
}
