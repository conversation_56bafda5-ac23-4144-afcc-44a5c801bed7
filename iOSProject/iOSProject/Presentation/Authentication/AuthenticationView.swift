import SwiftUI

extension AuthenticationView {
    static func build(container: DependencyContainer) -> some View {
        AuthenticationView(viewModel: AuthenticationViewModel(
            authUseCase: container.authUseCase,
            validationUseCase: container.validationUseCase,
            authNavigationUseCase: container.authNavigationUseCase
        ))
    }
}

// MARK: - Authentication View

struct AuthenticationView: View {
    @StateObject private var viewModel: AuthenticationViewModel
    @Environment(\.appColors) var colors
    @EnvironmentObject var navigationService: NavigationService

    init(viewModel: AuthenticationViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    var body: some View {
        GeometryReader { geometry in
            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: 32.h) {
                    Spacer(minLength: 44.h)

                    // Illustration Section
                    illustrationSection

                    // Header Section
                    headerSection

                    // Login Form
                    loginFormSection

                    // Social Login Section
                    socialLoginSection

                    // Sign Up Section
                    signUpSection

                    Spacer(minLength: 32.h)
                }
                .padding(.horizontal, 16.h)
                .frame(maxWidth: 400.h)
                .frame(minHeight: geometry.size.height)
            }
            .scrollIndicators(.hidden)
            .frame(maxWidth: .infinity)
        }
        .themedBackground()
        .alert("Error", isPresented: $viewModel.showError) {
            Button("OK") {}
        } message: {
            Text(viewModel.errorMessage)
        }
    }

    // MARK: - Illustration Section

    private var illustrationSection: some View {
        VStack {
            Image("Illustration")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 152.h, height: 118.h)
        }
    }

    // MARK: - Header Section

    private var headerSection: some View {
        VStack(spacing: 8.h) {
            Text("Login to you account")
                .headline24SemiBold()
                .themedTextColor(.primary)
                .multilineTextAlignment(.center)

            Text("Welcome back! Please enter your details.")
                .body14Regular()
                .themedTextColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    // MARK: - Login Form Section

    private var loginFormSection: some View {
        VStack(spacing: 24.h) {
            // Email Field
            emailField

            // Password Field
            passwordField

            // Forgot Password
            forgotPasswordSection

            // Login Button
            loginButton
        }
    }

    // MARK: - Email Field

    private var emailField: some View {
        VStack(alignment: .leading, spacing: 8.h) {
            Text("Email")
                .body12Medium()
                .themedTextColor(.primary)

            CustomInputField(
                text: $viewModel.email,
                placeholder: "Enter your email",
                keyboardType: .emailAddress,
                leadingIcon: "envelope"
            )

            if let emailError = viewModel.emailError {
                Text(emailError)
                    .body12Regular()
                    .foregroundColor(colors.error)
            }
        }
    }

    // MARK: - Password Field

    private var passwordField: some View {
        VStack(alignment: .leading, spacing: 8.h) {
            Text("Password")
                .body12Medium()
                .themedTextColor(.primary)

            CustomInputField(
                text: $viewModel.password,
                placeholder: "Enter password",
                isSecure: !viewModel.isPasswordVisible,

                onTrailingIconTap: {
                    viewModel.togglePasswordVisibility()
                }
            )

            if let passwordError = viewModel.passwordError {
                Text(passwordError)
                    .body12Regular()
                    .foregroundColor(colors.error)
            }
        }
    }

    // MARK: - Forgot Password Section

    private var forgotPasswordSection: some View {
        HStack {
            Spacer()
            Button(action: {
                viewModel.forgotPasswordTapped()
            }) {
                Text("Forgot password?")
                    .body12SemiBold()
                    .themedTextColor(.brand)
            }
        }
    }

    // MARK: - Login Button

    private var loginButton: some View {
        CustomButton(
            title: "Log in",
            isLoading: viewModel.isLoading,
            action: viewModel.loginTapped
        )
        .animation(.bouncy, value: viewModel.isLoading)
    }

    // MARK: - Social Login Section

    private var socialLoginSection: some View {
        VStack(spacing: 12.h) {
            CustomButton(
                title: "Log in with Google",
                variant: .outlined,
                leadingIcon: "GoogleLogo",
                action: {
                    viewModel.googleLoginTapped()
                }
            )

            CustomButton(
                title: "Log in with Apple",
                variant: .outlined,
                leadingIcon: "AppleLogo",
                action: {
                    viewModel.appleLoginTapped()
                }
            )
        }
    }

    // MARK: - Sign Up Section

    private var signUpSection: some View {
        HStack(spacing: 4.h) {
            Text("Don't have an account?")
                .body12Regular()
                .themedTextColor(.secondary)

            Button(action: {
                viewModel.signUpTapped()
            }) {
                Text("Sign up")
                    .body12SemiBold()
                    .themedTextColor(.brand)
            }
        }
    }
}

// MARK: - Preview

struct AuthenticationView_Previews: PreviewProvider {
    static var previews: some View {
        AuthenticationView(viewModel: AuthenticationViewModel(
            authUseCase: DependencyContainer().authUseCase,
            validationUseCase: DependencyContainer().validationUseCase,
            authNavigationUseCase: DependencyContainer().authNavigationUseCase
        ))
        .environmentObject(ThemeManager())
        .environmentObject(DependencyContainer())
        .environmentObject(NavigationService())
    }
}
