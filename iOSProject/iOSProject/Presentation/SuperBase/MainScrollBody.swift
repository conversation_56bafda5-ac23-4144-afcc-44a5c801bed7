//
//  MainScrollBody.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import SwiftUI

 struct MainScrollBody<Content>: View where Content: View {
     @Environment(\.appColors) var colors
     let content: Content
     
     init( @ViewBuilder content: () -> Content) {
         self.content = content()
     }
     
    var body: some View {
        ScrollView(.vertical) {
            self.content
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .logoNavigationBar(colors: colors)
        }
        .scrollIndicators(.hidden)
    }
}



extension View {
    func logoNavigationBar(colors:AppColorsProtocol) -> some View {
        toolbar {
           

            // Leading Item (Side Menu or Back Button)
            ToolbarItem(placement: .topBarLeading) {
                Image(ImageConstants.imgLogomark)
                    .resizable()
                    .frame(width: 32.h, height: 32.h)
            }

     

            // Trailing Content (Custom injected content)
            ToolbarItem(placement: .topBarTrailing) {
                // Menu button
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        AppStateManager.shared.toggleDrawer()
                    }
                }) {
                    Image(ImageConstants.imgMenu)
                        .resizable()
                        .frame(width: 24.h, height: 24.h)
                        .foregroundColor(colors.onSurface)
                }
            }
        }
    }
}
