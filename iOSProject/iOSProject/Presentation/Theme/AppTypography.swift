import SwiftUI


// MARK: - Custom Fonts
extension Font {
    // Inter Font Family
    static func interRegular(size: CGFloat) -> Font {
        return Font.custom("Inter-Regular", size: size)
    }
    
    static func interMedium(size: CGFloat) -> Font {
        return Font.custom("Inter-Medium", size: size)
    }
    
    static func interSemiBold(size: CGFloat) -> Font {
        return Font.custom("Inter-SemiBold", size: size)
    }
    
    // Poppins Font Family
    static func poppinsRegular(size: CGFloat) -> Font {
        return Font.custom("Poppins-Regular", size: size)
    }
    
    static func poppinsMedium(size: CGFloat) -> Font {
        return Font.custom("Poppins-Medium", size: size)
    }
}

// MARK: - Typography System
struct AppTypography {
    
    // MARK: - Display Styles
    // Large text styles for prominent headings
    static let display36SemiBold = Font.interSemiBold(size: 36)
    static let display30SemiBold = Font.interSemiBold(size: 30)
    
    // MARK: - Headline Styles
    // Medium-large text styles for section headers
    static let headline24SemiBold = Font.interSemiBold(size: 24)
    static let headline20SemiBold = Font.interSemiBold(size: 20)
    static let headline18Medium = Font.interMedium(size: 18)
    static let headline18Regular = Font.interRegular(size: 18)
    
    // MARK: - Title Styles
    // Medium text styles for titles and subtitles
    static let title20Regular = Font.system(size: 20, weight: .regular) // Roboto fallback
    static let title18MediumPoppins = Font.poppinsMedium(size: 18)
    static let title16Medium = Font.interMedium(size: 16)
    static let title16Regular = Font.interRegular(size: 16)
    static let title16RegularPoppins = Font.poppinsRegular(size: 16)
    static let title16 = Font.interRegular(size: 16)
    
    // MARK: - Body Styles
    // Standard text styles for body content
    static let body14Medium = Font.interMedium(size: 14)
    static let body14Regular = Font.interRegular(size: 14)
    static let body12Medium = Font.interMedium(size: 12)
    static let body12Regular = Font.interRegular(size: 12)
    static let body12SemiBold = Font.interSemiBold(size: 12)
    
    // MARK: - Caption Styles
    // Small text styles for captions and labels
    static let caption10Regular = Font.interRegular(size: 10)
    static let caption10Medium = Font.interMedium(size: 10)
}

// MARK: - Text Style Modifiers
extension Text {
    // Display Styles
    func display36SemiBold() -> some View {
        self.font(AppTypography.display36SemiBold)
    }
    
    func display30SemiBold() -> some View {
        self.font(AppTypography.display30SemiBold)
    }
    
    // Headline Styles
    func headline24SemiBold() -> some View {
        self.font(AppTypography.headline24SemiBold)
    }
    
    func headline20SemiBold() -> some View {
        self.font(AppTypography.headline20SemiBold)
    }
    
    func headline18Medium() -> some View {
        self.font(AppTypography.headline18Medium)
    }
    
    func headline18Regular() -> some View {
        self.font(AppTypography.headline18Regular)
    }
    
    // Title Styles
    func title18MediumPoppins() -> some View {
        self.font(AppTypography.title18MediumPoppins)
    }
    
    func title16Medium() -> some View {
        self.font(AppTypography.title16Medium)
    }
    
    func title16Regular() -> some View {
        self.font(AppTypography.title16Regular)
    }
    
    func title16RegularPoppins() -> some View {
        self.font(AppTypography.title16RegularPoppins)
    }
    
    // Body Styles
    func body14Medium() -> some View {
        self.font(AppTypography.body14Medium)
    }
    
    func body14Regular() -> some View {
        self.font(AppTypography.body14Regular)
    }
    
    func body12Medium() -> some View {
        self.font(AppTypography.body12Medium)
    }
    
    func body12Regular() -> some View {
        self.font(AppTypography.body12Regular)
    }
    
    func body12SemiBold() -> some View {
        self.font(AppTypography.body12SemiBold)
    }

    func body14SemiBold() -> some View {
        self.font(AppTypography.body14Medium) // Using medium as semibold equivalent
    }

    // Caption Styles
    func caption10Regular() -> some View {
        self.font(AppTypography.caption10Regular)
    }
    
    func caption10Medium() -> some View {
        self.font(AppTypography.caption10Medium)
    }
}

// MARK: - Line Height Support
extension Text {
    func lineHeight(_ height: CGFloat) -> some View {
        self.lineSpacing(height - 16) // Default line spacing
    }
}

// MARK: - Font Extension for Line Height
extension Font {
    var lineHeight: CGFloat {
        switch self {
        case AppTypography.display36SemiBold:
            return 45 // 1.25 ratio
        case AppTypography.display30SemiBold:
            return 37.5 // 1.25 ratio
        case AppTypography.headline24SemiBold:
            return 30 // 1.25 ratio
        case AppTypography.headline20SemiBold:
            return 25 // 1.25 ratio
        case AppTypography.headline18Medium, AppTypography.headline18Regular:
            return 22.5 // 1.25 ratio
        case AppTypography.title16Medium, AppTypography.title16Regular, AppTypography.title16RegularPoppins:
            return 20 // 1.25 ratio
        case AppTypography.body14Medium, AppTypography.body14Regular:
            return 17 // 1.21 ratio
        case AppTypography.body12Medium, AppTypography.body12Regular, AppTypography.body12SemiBold:
            return 15 // 1.25 ratio
        case AppTypography.caption10Regular, AppTypography.caption10Medium:
            return 12.5 // 1.25 ratio
        default:
            return 16 // Default line height
        }
    }
}

// MARK: - View Extensions for Typography
extension View {
    func body14Regular() -> some View {
        self.font(AppTypography.body14Regular)
    }

    func body14SemiBold() -> some View {
        self.font(AppTypography.body14Medium) // Using medium as semibold equivalent
    }
}
