//
//  AppStateManager.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import Foundation

// MARK: - State Persistence Helper
@MainActor
class AppStateManager: ObservableObject {
    static let shared = AppStateManager()
    
    @Published var selectedTab: Tab = .home
    @Published var isDrawerOpen: Bool = false
    
    private let userDefaults = UserDefaults.standard
    
    private enum Keys {
        static let selectedTab = "selected_tab"
    }
    
    private init() {
//        selectedTab = userDefaults.integer(forKey: Keys.selectedTab)
    }
    
    func updateSelectedTab(_ tab: Tab) {
        selectedTab = tab
        userDefaults.set(tab, forKey: Keys.selectedTab)
    }
    
    func openDrawer() {
        isDrawerOpen = true
    }
    
    func closeDrawer() {
        isDrawerOpen = false
    }
    
    func toggleDrawer() {
        isDrawerOpen.toggle()
    }
}
