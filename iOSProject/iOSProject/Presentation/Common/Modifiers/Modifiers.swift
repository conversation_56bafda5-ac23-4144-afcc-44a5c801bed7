//
//  Modifiers.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import SwiftUI

struct EnvironmentModifier: ViewModifier {
    @StateObject private var themeManager = ThemeManager()
    @StateObject private var dependencyContainer = DependencyContainer()
    @StateObject private var navigationService = NavigationService()
    @StateObject private var stateManager = AppStateManager.shared

    func body(content: Content) -> some View {
        content
            .environmentObject(stateManager)
            .environmentObject(navigationService)
            .environmentObject(themeManager)
            .environmentObject(dependencyContainer)
            .appTheme(themeManager)
            .onReceive(themeManager.$currentColors) { colors in
                // Update environment when theme changes
            }
    }
}
