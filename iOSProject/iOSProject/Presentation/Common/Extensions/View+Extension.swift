//
//  View+Extension.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import SwiftUI

extension View {
    func attachAllEnvironmentObjects() -> some View {
        modifier(EnvironmentModifier())
    }

    func hideNavigationBar(_ hide: Bool = true) -> some View {
        toolbar(hide ? .hidden : .visible, for: .tabBar)
            .toolbar(hide ? .hidden : .visible, for: .navigationBar)
            .navigationBarTitle("", displayMode: .inline)
            .navigationBarBackButtonHidden()
    }
}
