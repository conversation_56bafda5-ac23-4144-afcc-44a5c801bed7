import SwiftUI
import Combine

// MARK: - Profile View Model
@MainActor
class ProfileViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var user: UserProfile = UserProfile.defaultUser
    @Published var isLoading = false
    @Published var showError = false
    @Published var errorMessage = ""
    @Published var isEnglish = true
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    private let navigationCoordinator = NavigationCoordinator.shared
    
    // MARK: - Keys
    private enum Keys {
        static let language = "selected_language"
        static let userProfile = "user_profile"
    }
    
    // MARK: - Initialization
    init() {
        loadUserProfile()
        loadLanguagePreference()
    }
    
    // MARK: - User Profile Management
    func loadUserProfile() {
        isLoading = true

        // Simulate loading from local storage or API
        Task {
            do {
                try await Task.sleep(nanoseconds: 150_000_000) // 0.15 seconds

                if let data = userDefaults.data(forKey: Keys.userProfile) {
                    do {
                        let profile = try JSONDecoder().decode(UserProfile.self, from: data)
                        user = profile
                    } catch {
                        // If decoding fails, use default user and show error
                        user = UserProfile.defaultUser
                        showErrorMessage("Failed to load saved profile, using defaults")
                    }
                } else {
                    user = UserProfile.defaultUser
                }
                isLoading = false
            } catch {
                isLoading = false
                showErrorMessage("Failed to load profile: \(error.localizedDescription)")
            }
        }
    }
    
    func updateUserProfile(_ profile: UserProfile) {
        user = profile
        saveUserProfile()
    }
    
    private func saveUserProfile() {
        do {
            let data = try JSONEncoder().encode(user)
            userDefaults.set(data, forKey: Keys.userProfile)
        } catch {
            showErrorMessage("Failed to save profile: \(error.localizedDescription)")
        }
    }

    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
    
    // MARK: - Language Management
    func loadLanguagePreference() {
        let savedLanguage = userDefaults.string(forKey: Keys.language) ?? "en"
        isEnglish = savedLanguage == "en"
    }
    
    func changeLanguage(to language: String) {
        isEnglish = language == "en"
        userDefaults.set(language, forKey: Keys.language)
        
        // Notify other parts of the app about language change
        NotificationCenter.default.post(name: .languageChanged, object: language)
    }
    
    // MARK: - Profile Actions
    func editProfileImage() {
        // Handle profile image editing
        print("Edit profile image tapped")
    }
    
    func navigateToEditProfile() {
        navigationCoordinator.navigateToEditProfile()
    }

    func navigateToAddress() {
        navigationCoordinator.navigateToAddress()
    }

    func navigateToHistory() {
        navigationCoordinator.navigateToHistory()
    }

    func navigateToComplain() {
        navigationCoordinator.navigateToComplain()
    }

    func navigateToReferral() {
        navigationCoordinator.navigateToReferral()
    }

    func navigateToAboutUs() {
        navigationCoordinator.navigateToAboutUs()
    }

    func navigateToSettings() {
        navigationCoordinator.navigateToSettings()
    }

    func navigateToHelpSupport() {
        navigationCoordinator.navigateToHelpSupport()
    }
    
    func logout() {
        // Handle logout
        isLoading = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            // Clear user data
            self.userDefaults.removeObject(forKey: Keys.userProfile)
            
            // Navigate to authentication
            NotificationCenter.default.post(name: .userLoggedOut, object: nil)
            
            self.isLoading = false
        }
    }
}

// MARK: - User Profile Model
struct UserProfile: Codable, Equatable {
    let id: String
    var name: String
    var email: String
    var profileImageURL: String?
    var walletBalance: String
    
    static let defaultUser = UserProfile(
        id: "OP8761",
        name: "John Doe",
        email: "<EMAIL>",
        profileImageURL: nil,
        walletBalance: "50.00"
    )
}

// MARK: - Notification Names
extension Notification.Name {
    static let languageChanged = Notification.Name("languageChanged")
    static let userLoggedOut = Notification.Name("userLoggedOut")
    static let themeChanged = Notification.Name("themeChanged")
}

// MARK: - Profile Menu Item Model
struct ProfileMenuItem {
    let id = UUID()
    let icon: String
    let title: String
    let isLogout: Bool
    let action: () -> Void
    
    init(icon: String, title: String, isLogout: Bool = false, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.isLogout = isLogout
        self.action = action
    }
}

// MARK: - Profile Menu Items Factory
extension ProfileViewModel {
    func createMenuItems() -> [ProfileMenuItem] {
        [
            ProfileMenuItem(icon: ImageConstants.imgUser, title: "Edit Profile") {
                self.navigateToEditProfile()
            },
            ProfileMenuItem(icon: ImageConstants.imgSearchGray900_0c, title: "Address") {
                self.navigateToAddress()
            },
            ProfileMenuItem(icon: ImageConstants.imgHistory, title: "History") {
                self.navigateToHistory()
            },
            ProfileMenuItem(icon: ImageConstants.imgComplain, title: "Complain") {
                self.navigateToComplain()
            },
            ProfileMenuItem(icon: ImageConstants.imgReferral, title: "Referral") {
                self.navigateToReferral()
            },
            ProfileMenuItem(icon: ImageConstants.imgAboutUs, title: "About Us") {
                self.navigateToAboutUs()
            },
            ProfileMenuItem(icon: ImageConstants.imgSettings, title: "Settings") {
                self.navigateToSettings()
            },
            ProfileMenuItem(icon: ImageConstants.imgHelpAndSupport, title: "Help and Support") {
                self.navigateToHelpSupport()
            },
            ProfileMenuItem(icon: ImageConstants.imgLogout, title: "Logout", isLogout: true) {
                self.logout()
            }
        ]
    }
}

