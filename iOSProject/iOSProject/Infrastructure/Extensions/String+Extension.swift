//
//  View+Extension.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import SwiftUI

extension String {
    var localize:LocalizedStringKey { LocalizedStringKey(self) }
}


extension String {
    // MARK: - Check is valid Email

    func isValidEmail(isMandatory: Bool = false) -> Bool {
        if isEmpty() {
            return !isMandatory
        }
        
       
        let str = self.removeWhiteSpaces()
        let regExpression = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let predicate = NSPredicate(format: "SELF MATCHES %@", regExpression)
        return predicate.evaluate(with: str)
    }

    // MARK: - Removes whitespaces

    func removeWhiteSpaces() -> String {
        var str = self.trimmingCharacters(in: .whitespaces)
        str = str.replacingOccurrences(of: " ", with: "")
        return str
    }

    // MARK: - Check isEmpty

    func isEmpty() -> <PERSON>ol {
        if self.isEmpty || self == "" {
            return true
        }
        return false
    }
    
    var isEmptyInput: Bool {
        trimmed.isEmpty
    }
    
    var trimmed: String {
        trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

extension String {
    var toDouble: Double {
        return Double(self) ?? 0.0
    }
    
    /// Converts a string to a Date using the specified format.
       /// - Parameter format: A date format string (e.g. "yyyy-MM-dd")
       /// - Returns: A Date object if conversion succeeds, else nil.
       func toDate(format: String = "yyyy-MM-dd") -> Date? {
           let formatter = DateFormatter()
           formatter.dateFormat = format
           formatter.locale = Locale(identifier: "en_US_POSIX") // Ensures stable parsing
           formatter.timeZone = TimeZone(secondsFromGMT: 0)
           return formatter.date(from: self)
       }
}

