import Foundation

// MARK: - Validation Service
class ValidationService: ValidationServiceProtocol {
    
    func validateEmail(_ email: String) -> ValidationResult {
        // Check if email is empty
        guard !email.isEmpty else {
            return .invalid("Email is required")
        }
        
        // Check email format
        let emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        
        guard emailPredicate.evaluate(with: email) else {
            return .invalid("Please enter a valid email address")
        }
        
        // Check email length
        guard email.count <= 254 else {
            return .invalid("Email address is too long")
        }
        
        return .valid
    }
    
    func validatePassword(_ password: String) -> ValidationResult {
        // Check if password is empty
        guard !password.isEmpty else {
            return .invalid("Password is required")
        }
        
        // Check minimum length
        guard password.count >= 6 else {
            return .invalid("Password must be at least 6 characters long")
        }
        
        // Check maximum length
        guard password.count <= 128 else {
            return .invalid("Password is too long")
        }
        
        // Check for at least one letter
        let letterRegex = ".*[A-Za-z]+.*"
        let letterPredicate = NSPredicate(format: "SELF MATCHES %@", letterRegex)
        guard letterPredicate.evaluate(with: password) else {
            return .invalid("Password must contain at least one letter")
        }
        
        return .valid
    }
    
    // MARK: - Additional Validation Methods
    func validateName(_ name: String) -> ValidationResult {
        guard !name.isEmpty else {
            return .invalid("Name is required")
        }
        
        guard name.count >= 2 else {
            return .invalid("Name must be at least 2 characters long")
        }
        
        guard name.count <= 50 else {
            return .invalid("Name is too long")
        }
        
        // Check for valid characters (letters, spaces, hyphens, apostrophes)
        let nameRegex = "^[A-Za-z\\s\\-']+$"
        let namePredicate = NSPredicate(format: "SELF MATCHES %@", nameRegex)
        guard namePredicate.evaluate(with: name) else {
            return .invalid("Name can only contain letters, spaces, hyphens, and apostrophes")
        }
        
        return .valid
    }
    
    func validatePhoneNumber(_ phoneNumber: String) -> ValidationResult {
        guard !phoneNumber.isEmpty else {
            return .invalid("Phone number is required")
        }
        
        // Remove all non-digit characters for validation
        let digitsOnly = phoneNumber.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        
        guard digitsOnly.count >= 10 else {
            return .invalid("Phone number must be at least 10 digits")
        }
        
        guard digitsOnly.count <= 15 else {
            return .invalid("Phone number is too long")
        }
        
        return .valid
    }
    
    func validateCredentials(email: String, password: String) -> (email: ValidationResult, password: ValidationResult) {
        return (
            email: validateEmail(email),
            password: validatePassword(password)
        )
    }
}
